# ComfyUI Triangle Splatting Custom Nodes

This package provides ComfyUI custom nodes for the Triangle Splatting project, enabling real-time radiance field rendering using 3D triangles as rendering primitives.

## Features

- **Triangle Splatting Trainer**: Train Triangle Splatting models from image datasets
- **Triangle Splatting Renderer**: Render novel views from trained models
- **Triangle Splatting Video Creator**: Generate videos with camera trajectories
- **Triangle Splatting Mesh Exporter**: Export trained models to mesh formats (.off)
- **Triangle Splatting Evaluator**: Evaluate model quality with metrics

## Installation

### Prerequisites

1. **ComfyUI**: Make sure you have ComfyUI installed and working
2. **CUDA**: CUDA 12.6 or compatible version
3. **Python**: Python 3.11 recommended

### Setup Steps

1. **Clone the Triangle Splatting repository** (if not already done):
   ```bash
   git clone https://github.com/trianglesplatting/triangle-splatting --recursive
   cd triangle-splatting
   ```

2. **Install dependencies**:
   ```bash
   micromamba create -f requirements.yaml
   micromamba activate triangle-splatting
   ```

3. **Compile CUDA kernels**:
   ```bash
   bash compile.sh
   cd submodules/simple-knn
   pip install .
   cd ../..
   ```

4. **Install ComfyUI nodes**:
   - Copy the `comfyui_triangle_splatting` folder to your ComfyUI `custom_nodes` directory:
   ```bash
   cp -r comfyui_triangle_splatting /path/to/ComfyUI/custom_nodes/
   ```
   
   Or create a symbolic link:
   ```bash
   ln -s $(pwd)/comfyui_triangle_splatting /path/to/ComfyUI/custom_nodes/
   ```

5. **Restart ComfyUI** to load the new nodes

## Node Documentation

### Triangle Splatting Trainer

Trains a Triangle Splatting model from a dataset of images.

**Inputs:**
- `source_path`: Path to the dataset (COLMAP format)
- `model_path`: Output directory for the trained model
- `iterations`: Number of training iterations (default: 30000)
- `sh_degree`: Spherical harmonics degree (default: 3)
- `resolution`: Training resolution (-1 for auto, default: -1)
- `outdoor`: Enable outdoor scene optimizations (default: False)
- `white_background`: Use white background (default: False)
- `eval`: Enable evaluation during training (default: True)

**Advanced Parameters:**
- `lambda_dssim`: SSIM loss weight (default: 0.2)
- `feature_lr`: Feature learning rate (default: 0.0025)
- `opacity_lr`: Opacity learning rate (default: 0.014)
- `triangle_size`: Initial triangle size (default: 2.23)
- `set_opacity`: Initial opacity value (default: 0.28)
- `lambda_normals`: Normal regularization weight (default: 0.0001)
- `max_shapes`: Maximum number of triangles (default: 4000000)

**Outputs:**
- `model_path`: Path to the trained model
- `status`: Training status message

### Triangle Splatting Renderer

Renders novel views from a trained Triangle Splatting model.

**Inputs:**
- `model_path`: Path to the trained model
- `iteration`: Model iteration to load (-1 for latest, default: -1)
- `skip_train`: Skip rendering training views (default: False)
- `skip_test`: Skip rendering test views (default: False)
- `white_background`: Use white background (default: False)

**Outputs:**
- `output_path`: Path to rendered images
- `status`: Rendering status message

### Triangle Splatting Video Creator

Creates videos with camera trajectories from trained models.

**Inputs:**
- `model_path`: Path to the trained model
- `fps`: Video frame rate (default: 30)
- `duration`: Video duration in seconds (default: 5.0)
- `resolution`: Video resolution (default: 1080)

**Outputs:**
- `video_path`: Path to the generated video
- `status`: Video creation status message

### Triangle Splatting Mesh Exporter

Exports trained Triangle Splatting models to mesh formats for use in game engines.

**Inputs:**
- `model_path`: Path to the trained model
- `output_filename`: Output mesh filename (default: "mesh_colored.off")
- `include_opacity`: Include per-triangle opacity (default: False)

**Outputs:**
- `mesh_path`: Path to the exported mesh
- `status`: Export status message

### Triangle Splatting Evaluator

Evaluates the quality of trained Triangle Splatting models.

**Inputs:**
- `model_path`: Path to the trained model

**Outputs:**
- `metrics`: Evaluation metrics (PSNR, SSIM, LPIPS)
- `status`: Evaluation status message

## Usage Examples

### Basic Training Workflow

1. **Prepare Dataset**: Ensure your dataset is in COLMAP format with images and camera poses
2. **Add Triangle Splatting Trainer node**: Set source_path to your dataset and model_path to output directory
3. **Configure Parameters**: Adjust iterations, resolution, and other parameters as needed
4. **Run Training**: Execute the workflow to train the model

### Rendering Workflow

1. **Add Triangle Splatting Renderer node**: Set model_path to your trained model
2. **Configure Rendering**: Choose iteration and rendering options
3. **Run Rendering**: Execute to generate novel view images

### Video Creation Workflow

1. **Add Triangle Splatting Video Creator node**: Set model_path to your trained model
2. **Configure Video**: Set fps, duration, and resolution
3. **Run Video Creation**: Execute to generate a video

## Dataset Format

Triangle Splatting expects datasets in COLMAP format:
```
dataset/
├── images/           # Input images
├── sparse/           # COLMAP sparse reconstruction
│   └── 0/
│       ├── cameras.bin
│       ├── images.bin
│       └── points3D.bin
└── distorted/        # Optional: distorted images
```

## Troubleshooting

### Common Issues

1. **CUDA Compilation Errors**: Ensure CUDA toolkit is properly installed and compatible
2. **Memory Issues**: Reduce `max_shapes` or `resolution` for large scenes
3. **Import Errors**: Make sure the triangle-splatting environment is activated in ComfyUI

### Performance Tips

- Use `outdoor=True` for outdoor scenes
- Adjust `triangle_size` based on scene scale
- Increase `iterations` for higher quality results
- Use `white_background=True` for objects with transparent backgrounds

## License

This ComfyUI integration follows the same license as the Triangle Splatting project. See LICENSE.md and LICENSE_GS.md for details.

## Citation

If you use this work, please cite the original Triangle Splatting paper:

```bibtex
@article{Held2025Triangle,
title = {Triangle Splatting for Real-Time Radiance Field Rendering},
author = {Held, Jan and Vandeghen, Renaud and Deliege, Adrien and Hamdi, Abdullah and Cioppa, Anthony and Giancola, Silvio and Vedaldi, Andrea and Ghanem, Bernard and Tagliasacchi, Andrea and Van Droogenbroeck, Marc},
journal = {arXiv},
year = {2025},
}
```

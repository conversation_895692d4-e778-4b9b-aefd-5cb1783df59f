import os
import sys
import torch
import subprocess
import tempfile
from pathlib import Path
import json
import numpy as np
from PIL import Image

# Add the triangle splatting directory to the path
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))

try:
    from arguments import ModelParams, PipelineParams, OptimizationParams
    from scene import Scene, TriangleModel
    from triangle_renderer import render
    from utils.general_utils import safe_state
    from utils.image_utils import psnr
    import lpips
except ImportError as e:
    print(f"Warning: Could not import triangle splatting modules: {e}")
    print("Make sure the triangle splatting environment is properly set up")


class TriangleSpattingTrainer:
    """
    ComfyUI node for training Triangle Splatting models
    """
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "source_path": ("STRING", {"default": "", "multiline": False}),
                "model_path": ("STRING", {"default": "", "multiline": False}),
                "iterations": ("INT", {"default": 30000, "min": 1000, "max": 100000, "step": 1000}),
                "sh_degree": ("INT", {"default": 3, "min": 0, "max": 4}),
                "resolution": ("INT", {"default": -1, "min": -1, "max": 4096}),
                "outdoor": ("BOOLEAN", {"default": False}),
                "white_background": ("BOOLEAN", {"default": False}),
                "eval": ("BOOLEAN", {"default": True}),
            },
            "optional": {
                "lambda_dssim": ("FLOAT", {"default": 0.2, "min": 0.0, "max": 1.0, "step": 0.01}),
                "feature_lr": ("FLOAT", {"default": 0.0025, "min": 0.0001, "max": 0.01, "step": 0.0001}),
                "opacity_lr": ("FLOAT", {"default": 0.014, "min": 0.001, "max": 0.1, "step": 0.001}),
                "triangle_size": ("FLOAT", {"default": 2.23, "min": 0.1, "max": 10.0, "step": 0.1}),
                "set_opacity": ("FLOAT", {"default": 0.28, "min": 0.01, "max": 1.0, "step": 0.01}),
                "lambda_normals": ("FLOAT", {"default": 0.0001, "min": 0.0, "max": 0.01, "step": 0.0001}),
                "max_shapes": ("INT", {"default": 4000000, "min": 100000, "max": 10000000, "step": 100000}),
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING")
    RETURN_NAMES = ("model_path", "status")
    FUNCTION = "train_model"
    CATEGORY = "Triangle Splatting"
    
    def train_model(self, source_path, model_path, iterations, sh_degree, resolution, 
                   outdoor, white_background, eval, lambda_dssim=0.2, feature_lr=0.0025,
                   opacity_lr=0.014, triangle_size=2.23, set_opacity=0.28, 
                   lambda_normals=0.0001, max_shapes=4000000):
        
        try:
            # Validate paths
            if not os.path.exists(source_path):
                return ("", f"Error: Source path does not exist: {source_path}")
            
            # Create model directory if it doesn't exist
            os.makedirs(model_path, exist_ok=True)
            
            # Build command
            cmd = [
                sys.executable, 
                str(current_dir / "train.py"),
                "-s", source_path,
                "-m", model_path,
                "--iterations", str(iterations),
                "--sh_degree", str(sh_degree),
                "--resolution", str(resolution),
                "--lambda_dssim", str(lambda_dssim),
                "--feature_lr", str(feature_lr),
                "--opacity_lr", str(opacity_lr),
                "--triangle_size", str(triangle_size),
                "--set_opacity", str(set_opacity),
                "--lambda_normals", str(lambda_normals),
                "--max_shapes", str(max_shapes),
            ]
            
            if outdoor:
                cmd.append("--outdoor")
            if white_background:
                cmd.append("--white_background")
            if eval:
                cmd.append("--eval")
            
            # Run training
            print(f"Starting Triangle Splatting training with command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(current_dir))
            
            if result.returncode == 0:
                status = f"Training completed successfully. Model saved to: {model_path}"
                print(status)
                return (model_path, status)
            else:
                error_msg = f"Training failed with return code {result.returncode}\nStderr: {result.stderr}\nStdout: {result.stdout}"
                print(error_msg)
                return ("", error_msg)
                
        except Exception as e:
            error_msg = f"Error during training: {str(e)}"
            print(error_msg)
            return ("", error_msg)


class TriangleSpattingRenderer:
    """
    ComfyUI node for rendering with trained Triangle Splatting models
    """
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "model_path": ("STRING", {"default": "", "multiline": False}),
                "iteration": ("INT", {"default": -1, "min": -1, "max": 100000}),
                "skip_train": ("BOOLEAN", {"default": False}),
                "skip_test": ("BOOLEAN", {"default": False}),
                "white_background": ("BOOLEAN", {"default": False}),
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING")
    RETURN_NAMES = ("output_path", "status")
    FUNCTION = "render_model"
    CATEGORY = "Triangle Splatting"
    
    def render_model(self, model_path, iteration, skip_train, skip_test, white_background):
        try:
            if not os.path.exists(model_path):
                return ("", f"Error: Model path does not exist: {model_path}")
            
            # Build command
            cmd = [
                sys.executable,
                str(current_dir / "render.py"),
                "-m", model_path,
                "--iteration", str(iteration),
            ]
            
            if skip_train:
                cmd.append("--skip_train")
            if skip_test:
                cmd.append("--skip_test")
            if white_background:
                cmd.append("--white_background")
            
            # Run rendering
            print(f"Starting Triangle Splatting rendering with command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(current_dir))
            
            if result.returncode == 0:
                output_path = os.path.join(model_path, "renders")
                status = f"Rendering completed successfully. Images saved to: {output_path}"
                print(status)
                return (output_path, status)
            else:
                error_msg = f"Rendering failed with return code {result.returncode}\nStderr: {result.stderr}\nStdout: {result.stdout}"
                print(error_msg)
                return ("", error_msg)
                
        except Exception as e:
            error_msg = f"Error during rendering: {str(e)}"
            print(error_msg)
            return ("", error_msg)


class TriangleSpattingVideoCreator:
    """
    ComfyUI node for creating videos from Triangle Splatting models
    """
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "model_path": ("STRING", {"default": "", "multiline": False}),
                "fps": ("INT", {"default": 30, "min": 1, "max": 60}),
                "duration": ("FLOAT", {"default": 5.0, "min": 1.0, "max": 30.0, "step": 0.5}),
                "resolution": ("INT", {"default": 1080, "min": 480, "max": 4096, "step": 120}),
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING")
    RETURN_NAMES = ("video_path", "status")
    FUNCTION = "create_video"
    CATEGORY = "Triangle Splatting"
    
    def create_video(self, model_path, fps, duration, resolution):
        try:
            if not os.path.exists(model_path):
                return ("", f"Error: Model path does not exist: {model_path}")
            
            # Build command
            cmd = [
                sys.executable,
                str(current_dir / "create_video.py"),
                "-m", model_path,
                "--fps", str(fps),
                "--duration", str(duration),
                "--resolution", str(resolution),
            ]
            
            # Run video creation
            print(f"Starting Triangle Splatting video creation with command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(current_dir))
            
            if result.returncode == 0:
                video_path = os.path.join(model_path, "video.mp4")
                status = f"Video creation completed successfully. Video saved to: {video_path}"
                print(status)
                return (video_path, status)
            else:
                error_msg = f"Video creation failed with return code {result.returncode}\nStderr: {result.stderr}\nStdout: {result.stdout}"
                print(error_msg)
                return ("", error_msg)
                
        except Exception as e:
            error_msg = f"Error during video creation: {str(e)}"
            print(error_msg)
            return ("", error_msg)


class TriangleSpattingMeshExporter:
    """
    ComfyUI node for exporting Triangle Splatting models to mesh formats
    """
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "model_path": ("STRING", {"default": "", "multiline": False}),
                "output_filename": ("STRING", {"default": "mesh_colored.off", "multiline": False}),
                "include_opacity": ("BOOLEAN", {"default": False}),
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING")
    RETURN_NAMES = ("mesh_path", "status")
    FUNCTION = "export_mesh"
    CATEGORY = "Triangle Splatting"
    
    def export_mesh(self, model_path, output_filename, include_opacity):
        try:
            if not os.path.exists(model_path):
                return ("", f"Error: Model path does not exist: {model_path}")
            
            # Build command
            cmd = [
                sys.executable,
                str(current_dir / "create_off.py"),
                "-m", model_path,
                "--output", output_filename,
            ]
            
            if include_opacity:
                cmd.append("--include_opacity")
            
            # Run mesh export
            print(f"Starting Triangle Splatting mesh export with command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(current_dir))
            
            if result.returncode == 0:
                mesh_path = os.path.join(model_path, output_filename)
                status = f"Mesh export completed successfully. Mesh saved to: {mesh_path}"
                print(status)
                return (mesh_path, status)
            else:
                error_msg = f"Mesh export failed with return code {result.returncode}\nStderr: {result.stderr}\nStdout: {result.stdout}"
                print(error_msg)
                return ("", error_msg)
                
        except Exception as e:
            error_msg = f"Error during mesh export: {str(e)}"
            print(error_msg)
            return ("", error_msg)


class TriangleSpattingEvaluator:
    """
    ComfyUI node for evaluating Triangle Splatting models
    """
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "model_path": ("STRING", {"default": "", "multiline": False}),
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING")
    RETURN_NAMES = ("metrics", "status")
    FUNCTION = "evaluate_model"
    CATEGORY = "Triangle Splatting"
    
    def evaluate_model(self, model_path):
        try:
            if not os.path.exists(model_path):
                return ("", f"Error: Model path does not exist: {model_path}")
            
            # Build command
            cmd = [
                sys.executable,
                str(current_dir / "metrics.py"),
                "-m", model_path,
            ]
            
            # Run evaluation
            print(f"Starting Triangle Splatting evaluation with command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(current_dir))
            
            if result.returncode == 0:
                metrics = result.stdout
                status = f"Evaluation completed successfully."
                print(status)
                return (metrics, status)
            else:
                error_msg = f"Evaluation failed with return code {result.returncode}\nStderr: {result.stderr}\nStdout: {result.stdout}"
                print(error_msg)
                return ("", error_msg)
                
        except Exception as e:
            error_msg = f"Error during evaluation: {str(e)}"
            print(error_msg)
            return ("", error_msg)

{"last_node_id": 5, "last_link_id": 4, "nodes": [{"id": 1, "type": "TriangleSpattingTrainer", "pos": [100, 100], "size": [400, 600], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "model_path", "type": "STRING", "links": [1, 3], "shape": 3, "slot_index": 0}, {"name": "status", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "TriangleSpattingTrainer"}, "widgets_values": ["/path/to/your/dataset", "/path/to/output/model", 30000, 3, -1, false, false, true, 0.2, 0.0025, 0.014, 2.23, 0.28, 0.0001, 4000000]}, {"id": 2, "type": "TriangleSpatting<PERSON><PERSON><PERSON>", "pos": [600, 100], "size": [350, 300], "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "model_path", "type": "STRING", "link": 1, "slot_index": 0}], "outputs": [{"name": "output_path", "type": "STRING", "links": null, "shape": 3}, {"name": "status", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "TriangleSpatting<PERSON><PERSON><PERSON>"}, "widgets_values": ["", -1, false, false, false]}, {"id": 3, "type": "TriangleSpattingVideoCreator", "pos": [600, 450], "size": [350, 250], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "model_path", "type": "STRING", "link": 3, "slot_index": 0}], "outputs": [{"name": "video_path", "type": "STRING", "links": null, "shape": 3}, {"name": "status", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "TriangleSpattingVideoCreator"}, "widgets_values": ["", 30, 5.0, 1080]}, {"id": 4, "type": "TriangleSpattingMeshExporter", "pos": [1000, 100], "size": [350, 200], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model_path", "type": "STRING", "link": 4, "slot_index": 0}], "outputs": [{"name": "mesh_path", "type": "STRING", "links": null, "shape": 3}, {"name": "status", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "TriangleSpattingMeshExporter"}, "widgets_values": ["", "mesh_colored.off", false]}, {"id": 5, "type": "TriangleSpattingEvaluator", "pos": [1000, 350], "size": [350, 150], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model_path", "type": "STRING", "link": 2, "slot_index": 0}], "outputs": [{"name": "metrics", "type": "STRING", "links": null, "shape": 3}, {"name": "status", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "TriangleSpattingEvaluator"}, "widgets_values": [""]}], "links": [[1, 1, 0, 2, 0, "STRING"], [2, 1, 0, 5, 0, "STRING"], [3, 1, 0, 3, 0, "STRING"], [4, 1, 0, 4, 0, "STRING"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}
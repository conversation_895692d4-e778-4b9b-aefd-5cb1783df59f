#!/usr/bin/env python3
"""
Setup script for ComfyUI Triangle Splatting custom nodes
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and return success status"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error running command: {cmd}")
            print(f"Stderr: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"Exception running command {cmd}: {e}")
        return False

def check_cuda():
    """Check if CUDA is available"""
    try:
        result = subprocess.run(["nvcc", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ CUDA toolkit found")
            return True
        else:
            print("✗ CUDA toolkit not found")
            return False
    except FileNotFoundError:
        print("✗ CUDA toolkit not found (nvcc not in PATH)")
        return False

def check_python_version():
    """Check Python version"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✓ Python {version.major}.{version.minor} is supported")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor} is not supported. Python 3.8+ required.")
        return False

def setup_environment():
    """Set up the Triangle Splatting environment"""
    print("Setting up Triangle Splatting environment...")
    
    # Get the triangle splatting root directory
    current_dir = Path(__file__).parent.parent
    
    # Check if requirements.yaml exists
    requirements_file = current_dir / "requirements.yaml"
    if not requirements_file.exists():
        print("✗ requirements.yaml not found. Make sure you're in the triangle-splatting directory.")
        return False
    
    # Create conda environment
    print("Creating conda environment...")
    if not run_command(f"micromamba create -f {requirements_file}", cwd=current_dir):
        print("Failed to create conda environment. Trying with conda...")
        if not run_command(f"conda env create -f {requirements_file}", cwd=current_dir):
            print("Failed to create environment with both micromamba and conda.")
            return False
    
    print("✓ Environment created successfully")
    return True

def compile_cuda_kernels():
    """Compile CUDA kernels"""
    print("Compiling CUDA kernels...")
    
    current_dir = Path(__file__).parent.parent
    
    # Run compile script
    compile_script = current_dir / "compile.sh"
    if compile_script.exists():
        if not run_command("bash compile.sh", cwd=current_dir):
            print("Failed to compile CUDA kernels with compile.sh")
            return False
    else:
        # Try compile.bat on Windows
        compile_bat = current_dir / "compile.bat"
        if compile_bat.exists():
            if not run_command("compile.bat", cwd=current_dir):
                print("Failed to compile CUDA kernels with compile.bat")
                return False
        else:
            print("No compile script found")
            return False
    
    # Install simple-knn
    simple_knn_dir = current_dir / "submodules" / "simple-knn"
    if simple_knn_dir.exists():
        print("Installing simple-knn...")
        if not run_command("pip install .", cwd=simple_knn_dir):
            print("Failed to install simple-knn")
            return False
    else:
        print("simple-knn submodule not found")
        return False
    
    print("✓ CUDA kernels compiled successfully")
    return True

def install_to_comfyui(comfyui_path):
    """Install nodes to ComfyUI"""
    if not comfyui_path:
        print("ComfyUI path not provided. Skipping installation.")
        return True
    
    comfyui_path = Path(comfyui_path)
    if not comfyui_path.exists():
        print(f"ComfyUI path does not exist: {comfyui_path}")
        return False
    
    custom_nodes_dir = comfyui_path / "custom_nodes"
    if not custom_nodes_dir.exists():
        print(f"ComfyUI custom_nodes directory not found: {custom_nodes_dir}")
        return False
    
    # Copy the nodes
    source_dir = Path(__file__).parent
    target_dir = custom_nodes_dir / "comfyui_triangle_splatting"
    
    try:
        if target_dir.exists():
            shutil.rmtree(target_dir)
        shutil.copytree(source_dir, target_dir)
        print(f"✓ Nodes installed to {target_dir}")
        return True
    except Exception as e:
        print(f"Failed to copy nodes: {e}")
        return False

def main():
    """Main setup function"""
    print("Triangle Splatting ComfyUI Setup")
    print("=" * 40)
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    if not check_cuda():
        print("Warning: CUDA not found. You may need to install CUDA toolkit.")
    
    # Setup environment
    setup_env = input("Set up Triangle Splatting environment? (y/n): ").lower().strip()
    if setup_env == 'y':
        if not setup_environment():
            print("Environment setup failed")
            return False
    
    # Compile CUDA kernels
    compile_cuda = input("Compile CUDA kernels? (y/n): ").lower().strip()
    if compile_cuda == 'y':
        if not compile_cuda_kernels():
            print("CUDA kernel compilation failed")
            return False
    
    # Install to ComfyUI
    install_nodes = input("Install nodes to ComfyUI? (y/n): ").lower().strip()
    if install_nodes == 'y':
        comfyui_path = input("Enter ComfyUI installation path: ").strip()
        if not install_to_comfyui(comfyui_path):
            print("Node installation failed")
            return False
    
    print("\n" + "=" * 40)
    print("Setup completed successfully!")
    print("\nNext steps:")
    print("1. Activate the triangle-splatting environment")
    print("2. Start ComfyUI")
    print("3. Look for Triangle Splatting nodes in the node menu")
    print("4. Load the example workflow from example_workflow.json")
    
    return True

if __name__ == "__main__":
    main()

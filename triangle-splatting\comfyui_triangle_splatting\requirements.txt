# ComfyUI Triangle Splatting Requirements
# These are additional requirements for the ComfyUI integration
# The main triangle splatting requirements are in ../requirements.yaml

# Core dependencies (should already be installed with triangle splatting)
torch>=2.0.0
torchvision>=0.15.0
numpy>=1.21.0
pillow>=8.0.0
tqdm>=4.60.0

# Additional dependencies for ComfyUI integration
pathlib2>=2.3.0  # For better path handling
psutil>=5.8.0    # For process monitoring

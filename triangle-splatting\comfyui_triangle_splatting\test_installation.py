#!/usr/bin/env python3
"""
Test script to verify Triangle Splatting ComfyUI installation
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
    except ImportError:
        print("✗ PyTorch not found")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError:
        print("✗ NumPy not found")
        return False
    
    try:
        from PIL import Image
        print("✓ Pillow")
    except ImportError:
        print("✗ Pillow not found")
        return False
    
    # Test triangle splatting imports
    current_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(current_dir))
    
    try:
        from arguments import ModelParams, PipelineParams, OptimizationParams
        print("✓ Triangle Splatting arguments")
    except ImportError as e:
        print(f"✗ Triangle Splatting arguments: {e}")
        return False
    
    try:
        from scene import Scene, TriangleModel
        print("✓ Triangle Splatting scene")
    except ImportError as e:
        print(f"✗ Triangle Splatting scene: {e}")
        return False
    
    try:
        from triangle_renderer import render
        print("✓ Triangle Splatting renderer")
    except ImportError as e:
        print(f"✗ Triangle Splatting renderer: {e}")
        return False
    
    return True

def test_cuda():
    """Test CUDA availability"""
    print("\nTesting CUDA...")
    
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ CUDA available: {torch.cuda.get_device_name(0)}")
            print(f"✓ CUDA version: {torch.version.cuda}")
            return True
        else:
            print("✗ CUDA not available")
            return False
    except Exception as e:
        print(f"✗ CUDA test failed: {e}")
        return False

def test_nodes():
    """Test if ComfyUI nodes can be imported"""
    print("\nTesting ComfyUI nodes...")
    
    try:
        from nodes import (
            TriangleSpattingTrainer,
            TriangleSpattingRenderer,
            TriangleSpattingVideoCreator,
            TriangleSpattingMeshExporter,
            TriangleSpattingEvaluator
        )
        print("✓ All ComfyUI nodes imported successfully")
        return True
    except ImportError as e:
        print(f"✗ ComfyUI nodes import failed: {e}")
        return False

def test_file_structure():
    """Test if all required files are present"""
    print("\nTesting file structure...")
    
    current_dir = Path(__file__).parent
    triangle_dir = current_dir.parent
    
    required_files = [
        triangle_dir / "train.py",
        triangle_dir / "render.py",
        triangle_dir / "create_video.py",
        triangle_dir / "create_off.py",
        triangle_dir / "metrics.py",
        triangle_dir / "requirements.yaml",
        current_dir / "__init__.py",
        current_dir / "nodes.py",
        current_dir / "README.md",
    ]
    
    all_present = True
    for file_path in required_files:
        if file_path.exists():
            print(f"✓ {file_path.name}")
        else:
            print(f"✗ {file_path.name} not found")
            all_present = False
    
    return all_present

def main():
    """Main test function"""
    print("Triangle Splatting ComfyUI Installation Test")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Python Imports", test_imports),
        ("CUDA", test_cuda),
        ("ComfyUI Nodes", test_nodes),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 20)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Summary:")
    print("-" * 20)
    
    all_passed = True
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Triangle Splatting ComfyUI is ready to use.")
        print("\nNext steps:")
        print("1. Start ComfyUI")
        print("2. Look for Triangle Splatting nodes in the node menu")
        print("3. Load the example workflow from example_workflow.json")
    else:
        print("❌ Some tests failed. Please check the installation.")
        print("\nTroubleshooting:")
        print("1. Make sure you've run the setup script")
        print("2. Activate the triangle-splatting conda environment")
        print("3. Compile the CUDA kernels")
        print("4. Check that all dependencies are installed")
    
    return all_passed

if __name__ == "__main__":
    main()
